# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: nacos.io
layout:
- go.kubebuilder.io/v4
projectName: nacos-controller
repo: nacos-controller
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nacos.io
  group: nacos.io
  kind: DynamicConfiguration
  path: nacos-controller/api/v1
  version: v1
- domain: nacos.io
  kind: DynamicConfiguration
  path: nacos-controller/api/v1
  version: v1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- controller: true
  domain: nacos.io
  kind: ConfigMap
  version: v1
- controller: true
  core: true
  group: core
  kind: Secret
  path: k8s.io/api/core/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nacos.io
  group: nacos.io
  kind: ServiceDiscovery
  path: nacos-controller/api/v1
  version: v1
- controller: true
  group: core
  kind: Endpoint
  path: k8s.io/api/core/v1
  version: v1
- controller: true
  group: core
  kind: Service
  path: k8s.io/api/core/v1
  version: v1
version: "3"
