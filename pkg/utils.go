package pkg

// Contains checks if the passed string is present in the given slice of strings.
func Contains(list []string, s string) bool {
	for _, v := range list {
		if v == s {
			return true
		}
	}
	return false
}

// Remove deletes the passed string from the given slice of strings.
func Remove(list []string, s string) []string {
	for i, v := range list {
		if v == s {
			list = append(list[:i], list[i+1:]...)
		}
	}
	return list
}

var CurrentContext = "null"
