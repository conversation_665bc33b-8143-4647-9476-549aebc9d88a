---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: servicediscoveries.nacos.io
spec:
  group: nacos.io
  names:
    kind: ServiceDiscovery
    listKind: ServiceDiscoveryList
    plural: servicediscoveries
    singular: servicediscovery
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: ServiceDiscovery is the Schema for the servicediscoveries API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ServiceDiscoverySpec defines the desired state of ServiceDiscovery
            properties:
              nacosServer:
                properties:
                  authRef:
                    description: ObjectReference contains enough information to let
                      you inspect or modify the referred object.
                    properties:
                      apiVersion:
                        description: API version of the referent.
                        type: string
                      fieldPath:
                        description: |-
                          If referring to a piece of an object instead of an entire object, this string
                          should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                          For example, if the object reference is to a container within a pod, this would take on a value like:
                          "spec.containers{name}" (where "name" refers to the name of the container that triggered
                          the event) or if no container name is specified "spec.containers[2]" (container with
                          index 2 in this pod). This syntax is chosen only to have some well-defined way of
                          referencing a part of an object.
                        type: string
                      kind:
                        description: |-
                          Kind of the referent.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                        type: string
                      name:
                        description: |-
                          Name of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                      namespace:
                        description: |-
                          Namespace of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                        type: string
                      resourceVersion:
                        description: |-
                          Specific resourceVersion to which this reference is made, if any.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                        type: string
                      uid:
                        description: |-
                          UID of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  endpoint:
                    type: string
                  namespace:
                    type: string
                  serverAddr:
                    type: string
                type: object
              objectRef:
                description: ObjectReference contains enough information to let you
                  inspect or modify the referred object.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              services:
                description: |-
                  INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                items:
                  type: string
                type: array
              syncDirect:
                type: string
            type: object
          status:
            description: ServiceDiscoveryStatus defines the observed state of ServiceDiscovery
            properties:
              message:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                type: string
              objectRef:
                description: ObjectReference contains enough information to let you
                  inspect or modify the referred object.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              syncStatuses:
                additionalProperties:
                  properties:
                    groupName:
                      type: string
                    lastSyncFrom:
                      type: string
                    lastSyncTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    ready:
                      type: boolean
                    serviceName:
                      type: string
                  type: object
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
