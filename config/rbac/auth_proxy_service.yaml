apiVersion: v1
kind: Service
metadata:
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: service
    app.kubernetes.io/instance: controller-manager-metrics-service
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/created-by: nacos-controller
    app.kubernetes.io/part-of: nacos-controller
    app.kubernetes.io/managed-by: kustomize
  name: controller-manager-metrics-service
  namespace: system
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    control-plane: controller-manager
