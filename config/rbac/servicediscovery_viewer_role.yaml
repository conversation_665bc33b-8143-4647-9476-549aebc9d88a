# permissions for end users to view servicediscoveries.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: servicediscovery-viewer-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: nacos-controller
    app.kubernetes.io/part-of: nacos-controller
    app.kubernetes.io/managed-by: kustomize
  name: servicediscovery-viewer-role
rules:
- apiGroups:
  - nacos.io.nacos.io
  resources:
  - servicediscoveries
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nacos.io.nacos.io
  resources:
  - servicediscoveries/status
  verbs:
  - get
