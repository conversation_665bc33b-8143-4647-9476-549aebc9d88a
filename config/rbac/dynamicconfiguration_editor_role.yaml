# permissions for end users to edit dynamicconfigurations.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: clusterrole
    app.kubernetes.io/instance: dynamicconfiguration-editor-role
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: nacos-controller
    app.kubernetes.io/part-of: nacos-controller
    app.kubernetes.io/managed-by: kustomize
  name: dynamicconfiguration-editor-role
rules:
- apiGroups:
  - nacos.io
  resources:
  - dynamicconfigurations
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nacos.io
  resources:
  - dynamicconfigurations/status
  verbs:
  - get
