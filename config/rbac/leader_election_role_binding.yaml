apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: rolebinding
    app.kubernetes.io/instance: leader-election-rolebinding
    app.kubernetes.io/component: rbac
    app.kubernetes.io/created-by: nacos-controller
    app.kubernetes.io/part-of: nacos-controller
    app.kubernetes.io/managed-by: kustomize
  name: leader-election-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: leader-election-role
subjects:
- kind: ServiceAccount
  name: controller-manager
  namespace: system
