---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "nacos-controller.fullname" . }}
rules:
  - apiGroups:
      - nacos.io
    resources:
      - '*'
    verbs:
      - '*'
  - apiGroups:
      - ""
    resources:
      - "configmaps"
      - "secrets"
      - "services"
      - "endpoints"
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - "coordination.k8s.io"
    resources:
      - "leases"
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "nacos-controller.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "nacos-controller.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "nacos-controller.fullname" . }}
    namespace: {{ .Release.Namespace }}