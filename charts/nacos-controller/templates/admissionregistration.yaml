{{- $ca := genCA "admission-controller-ca" 3650 }}
{{- $cn := printf "%s-%s" .Release.Name .Chart.Name }}
{{- $altName1 := printf "%s.%s" (default (include "nacos-controller.fullname" .) .Release.Name) .Release.Namespace }}
{{- $altName2 := printf "%s.%s.svc" (default (include "nacos-controller.fullname" .) .Release.Name) .Release.Namespace }}
{{- $cert := genSignedCert $cn nil (list $altName1 $altName2) 3650 $ca }}
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ include "nacos-controller.fullname" . }}
webhooks:
  - admissionReviewVersions:
      - v1
      - v1beta1
    clientConfig:
      caBundle: {{ b64enc $ca.Cert }}
      service:
        name: {{ include "nacos-controller.fullname" . }}
        namespace: {{ .Release.Namespace }}
        path: /validate-nacos-io-v1-dynamicconfiguration
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: dc.validating.nacos.io
    namespaceSelector: {}
    objectSelector: {}
    rules:
      - apiGroups:
          - nacos.io
        apiVersions:
          - v1
        operations:
          - CREATE
          - UPDATE
        resources:
          - dynamicconfigurations
        scope: '*'
    sideEffects: None
    timeoutSeconds: 5
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
  name: {{ include "nacos-controller.fullname" . }}
webhooks:
  - admissionReviewVersions:
      - v1
      - v1beta1
    clientConfig:
      caBundle: {{ b64enc $ca.Cert }}
      service:
        name: {{ include "nacos-controller.fullname" . }}
        namespace: {{ .Release.Namespace }}
        path: /mutate-nacos-io-v1-dynamicconfiguration
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: dc.mutating.nacos.io
    namespaceSelector: {}
    objectSelector: {}
    reinvocationPolicy: Never
    rules:
      - apiGroups:
          - nacos.io
        apiVersions:
          - v1
        operations:
          - CREATE
          - UPDATE
        resources:
          - dynamicconfigurations
        scope: '*'
    sideEffects: None
    timeoutSeconds: 5
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nacos-controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
type: Opaque
data:
  tls.crt: {{ b64enc $cert.Cert }}
  tls.key: {{ b64enc $cert.Key }}