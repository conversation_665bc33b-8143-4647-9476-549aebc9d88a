---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: dynamicconfigurations.nacos.io
spec:
  group: nacos.io
  names:
    kind: DynamicConfiguration
    listKind: DynamicConfigurationList
    plural: dynamicconfigurations
    shortNames:
      - dc
    singular: dynamicconfiguration
  scope: Namespaced
  versions:
    - additionalPrinterColumns:
        - jsonPath: .status.phase
          name: Phase
          type: string
        - jsonPath: .metadata.creationTimestamp
          name: Age
          type: date
      name: v1
      schema:
        openAPIV3Schema:
          description: DynamicConfiguration is the Schema for the dynamicconfigurations
            API
          properties:
            apiVersion:
              description: |-
                APIVersion defines the versioned schema of this representation of an object.
                Servers should convert recognized schemas to the latest internal value, and
                may reject unrecognized values.
                More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
              type: string
            kind:
              description: |-
                Kind is a string value representing the REST resource this object represents.
                Servers may infer this from the endpoint the client submits requests to.
                Cannot be updated.
                In CamelCase.
                More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
              type: string
            metadata:
              type: object
            spec:
              description: DynamicConfigurationSpec defines the desired state of DynamicConfiguration
              properties:
                additionalConf:
                  description: |-
                    INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                    Important: Run "make" to regenerate code after modifying this file
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      type: object
                    properties:
                      additionalProperties:
                        type: string
                      type: object
                    tags:
                      additionalProperties:
                        type: string
                      type: object
                  type: object
                nacosServer:
                  properties:
                    authRef:
                      description: ObjectReference contains enough information to let
                        you inspect or modify the referred object.
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        fieldPath:
                          description: |-
                            If referring to a piece of an object instead of an entire object, this string
                            should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                            For example, if the object reference is to a container within a pod, this would take on a value like:
                            "spec.containers{name}" (where "name" refers to the name of the container that triggered
                            the event) or if no container name is specified "spec.containers[2]" (container with
                            index 2 in this pod). This syntax is chosen only to have some well-defined way of
                            referencing a part of an object.
                          type: string
                        kind:
                          description: |-
                            Kind of the referent.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                          type: string
                        name:
                          description: |-
                            Name of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        namespace:
                          description: |-
                            Namespace of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                          type: string
                        resourceVersion:
                          description: |-
                            Specific resourceVersion to which this reference is made, if any.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                          type: string
                        uid:
                          description: |-
                            UID of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    endpoint:
                      type: string
                    namespace:
                      type: string
                    serverAddr:
                      type: string
                  type: object
                objectRefs:
                  items:
                    description: ObjectReference contains enough information to let
                      you inspect or modify the referred object.
                    properties:
                      apiVersion:
                        description: API version of the referent.
                        type: string
                      fieldPath:
                        description: |-
                          If referring to a piece of an object instead of an entire object, this string
                          should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                          For example, if the object reference is to a container within a pod, this would take on a value like:
                          "spec.containers{name}" (where "name" refers to the name of the container that triggered
                          the event) or if no container name is specified "spec.containers[2]" (container with
                          index 2 in this pod). This syntax is chosen only to have some well-defined way of
                          referencing a part of an object.
                        type: string
                      kind:
                        description: |-
                          Kind of the referent.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                        type: string
                      name:
                        description: |-
                          Name of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                      namespace:
                        description: |-
                          Namespace of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                        type: string
                      resourceVersion:
                        description: |-
                          Specific resourceVersion to which this reference is made, if any.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                        type: string
                      uid:
                        description: |-
                          UID of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  type: array
                strategy:
                  properties:
                    conflictPolicy:
                      type: string
                    scope:
                      type: string
                    syncDeletion:
                      default: false
                      type: boolean
                  type: object
              type: object
            status:
              description: DynamicConfigurationStatus defines the observed state of
                DynamicConfiguration
              properties:
                listenConfigs:
                  additionalProperties:
                    items:
                      type: string
                    type: array
                  type: object
                message:
                  type: string
                nacosServerStatus:
                  properties:
                    endpoint:
                      type: string
                    namespace:
                      type: string
                    serverAddr:
                      type: string
                  type: object
                observedGeneration:
                  format: int64
                  type: integer
                phase:
                  description: |-
                    INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                    Important: Run "make" to regenerate code after modifying this file
                  type: string
                syncStatuses:
                  additionalProperties:
                    items:
                      properties:
                        dataId:
                          type: string
                        lastSyncFrom:
                          type: string
                        lastSyncTime:
                          format: date-time
                          type: string
                        md5:
                          type: string
                        message:
                          type: string
                        ready:
                          type: boolean
                      type: object
                    type: array
                  type: object
                syncStrategyStatus:
                  properties:
                    conflictPolicy:
                      type: string
                    scope:
                      type: string
                    syncDeletion:
                      default: false
                      type: boolean
                  type: object
              type: object
          type: object
      served: true
      storage: true
      subresources:
        status: {}
