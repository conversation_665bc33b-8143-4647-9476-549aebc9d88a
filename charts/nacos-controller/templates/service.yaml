apiVersion: v1
kind: Service
metadata:
  name: {{ include "nacos-controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nacos-controller.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: webhook
      protocol: TCP
      name: http
  selector:
    {{- include "nacos-controller.selectorLabels" . | nindent 4 }}
